//
//  ContentView.swift
//  testAI
//
//  Created by <PERSON><PERSON><PERSON> on 20/9/25.
//

import SwiftUI

struct ContentView: View {
    var body: some View {
        ZStack {
            // Dark background
            Color.black
                .ignoresSafeArea()
            
            VStack(spacing: 0) {
                // Status bar
                HStack {
                    Text("01:44")
                        .foregroundColor(.white)
                        .font(.system(size: 17, weight: .semibold))
                    
                    Image(systemName: "bed.double.fill")
                        .foregroundColor(.white)
                        .font(.system(size: 14))
                    
                    Spacer()
                    
                    HStack(spacing: 4) {
                        Circle()
                            .fill(Color.gray)
                            .frame(width: 4, height: 4)
                        Circle()
                            .fill(Color.gray)
                            .frame(width: 4, height: 4)
                        Circle()
                            .fill(Color.gray)
                            .frame(width: 4, height: 4)
                        Circle()
                            .fill(Color.gray)
                            .frame(width: 4, height: 4)
                    }
                    
                    Image(systemName: "wifi")
                        .foregroundColor(.white)
                        .font(.system(size: 16))
                    
                    ZStack {
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(Color.white, lineWidth: 1)
                            .frame(width: 40, height: 22)
                        
                        Text("38")
                            .foregroundColor(.white)
                            .font(.system(size: 12, weight: .medium))
                    }
                }
                .padding(.horizontal, 20)
                .padding(.top, 10)
                
                // Back button
                HStack {
                    Button(action: {}) {
                        Image(systemName: "arrow.left")
                            .foregroundColor(.white)
                            .font(.system(size: 20))
                    }
                    Spacer()
                }
                .padding(.horizontal, 20)
                .padding(.top, 30)
                
                Spacer()
                
                // Live Place banner
                HStack {
                    Spacer()
                    
                    ZStack {
                        RoundedRectangle(cornerRadius: 25)
                            .fill(Color.gray.opacity(0.8))
                            .frame(height: 50)
                        
                        HStack {
                            Text("🌳")
                                .font(.system(size: 20))
                            
                            Text("创造我的Live Place")
                                .foregroundColor(.white)
                                .font(.system(size: 16, weight: .medium))
                        }
                        .padding(.horizontal, 20)
                        
                        HStack {
                            Spacer()
                            Circle()
                                .fill(Color.red)
                                .frame(width: 8, height: 8)
                                .offset(x: -10, y: -15)
                        }
                    }
                    .padding(.horizontal, 40)
                    
                    Spacer()
                }
                .padding(.bottom, 80)
                
                // Profile section
                VStack(spacing: 20) {
                    // Avatar
                    ZStack {
                        Circle()
                            .fill(LinearGradient(
                                gradient: Gradient(colors: [Color.orange.opacity(0.3), Color.yellow]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ))
                            .frame(width: 120, height: 120)
                        
                        Text("回家")
                            .foregroundColor(.brown)
                            .font(.system(size: 32, weight: .bold))
                    }
                    
                    // Name and username
                    VStack(spacing: 8) {
                        Text("回家 凝聚力")
                            .foregroundColor(.white)
                            .font(.system(size: 24, weight: .medium))
                        
                        HStack {
                            Text("@buz65098275")
                                .foregroundColor(.gray)
                                .font(.system(size: 16))
                                .padding(.horizontal, 12)
                                .padding(.vertical, 6)
                                .background(
                                    RoundedRectangle(cornerRadius: 15)
                                        .fill(Color.gray.opacity(0.3))
                                )
                            
                            Image(systemName: "doc.on.clipboard")
                                .foregroundColor(.gray)
                                .font(.system(size: 16))
                        }
                    }
                    
                    // Action buttons
                    HStack(spacing: 60) {
                        VStack(spacing: 8) {
                            Image(systemName: "square.and.arrow.up")
                                .foregroundColor(.white)
                                .font(.system(size: 24))
                            Text("分享")
                                .foregroundColor(.gray)
                                .font(.system(size: 14))
                        }
                        
                        VStack(spacing: 8) {
                            Image(systemName: "person")
                                .foregroundColor(.white)
                                .font(.system(size: 24))
                            Text("账户")
                                .foregroundColor(.gray)
                                .font(.system(size: 14))
                        }
                        
                        VStack(spacing: 8) {
                            Image(systemName: "qrcode")
                                .foregroundColor(.white)
                                .font(.system(size: 24))
                            Text("二维码")
                                .foregroundColor(.gray)
                                .font(.system(size: 14))
                        }
                    }
                    .padding(.top, 20)
                }
                
                Spacer()
                
                // Phone verification section
                HStack {
                    HStack {
                        Image(systemName: "phone")
                            .foregroundColor(.white)
                            .font(.system(size: 18))
                        
                        VStack(alignment: .leading, spacing: 2) {
                            Text("验证手机号")
                                .foregroundColor(.white)
                                .font(.system(size: 16, weight: .medium))
                            Text("你的朋友可以更容易找到你")
                                .foregroundColor(.gray)
                                .font(.system(size: 14))
                        }
                    }
                    
                    Spacer()
                    
                    HStack(spacing: 15) {
                        Button(action: {}) {
                            Text("验证")
                                .foregroundColor(.black)
                                .font(.system(size: 14, weight: .medium))
                                .padding(.horizontal, 20)
                                .padding(.vertical, 8)
                                .background(Color.white)
                                .clipShape(RoundedRectangle(cornerRadius: 20))
                        }
                        
                        Button(action: {}) {
                            Image(systemName: "xmark")
                                .foregroundColor(.gray)
                                .font(.system(size: 16))
                        }
                    }
                }
                .padding(20)
                .background(
                    RoundedRectangle(cornerRadius: 15)
                        .fill(Color.gray.opacity(0.3))
                )
                .padding(.horizontal, 20)
                .padding(.bottom, 30)
                
                // Menu items
                VStack(spacing: 0) {
                    MenuRow(icon: "message", iconColor: .green, title: "消息设置")
                    MenuRow(icon: "rectangle.on.rectangle", iconColor: .green, title: "Buz快捷方式")
                    MenuRow(icon: "applewatch", iconColor: .green, title: "Buz on Watch")
                }
                .padding(.horizontal, 20)
                
                Spacer()
                
                // Home indicator
                RoundedRectangle(cornerRadius: 2.5)
                    .fill(Color.white)
                    .frame(width: 134, height: 5)
                    .padding(.bottom, 8)
            }
        }
    }
}

struct MenuRow: View {
    let icon: String
    let iconColor: Color
    let title: String
    
    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(iconColor)
                .font(.system(size: 18))
                .frame(width: 30)
            
            Text(title)
                .foregroundColor(.white)
                .font(.system(size: 16))
            
            Spacer()
            
            Image(systemName: "chevron.right")
                .foregroundColor(.gray)
                .font(.system(size: 14))
        }
        .padding(.vertical, 15)
        .padding(.horizontal, 20)
    }
}

#Preview {
    ContentView()
}
