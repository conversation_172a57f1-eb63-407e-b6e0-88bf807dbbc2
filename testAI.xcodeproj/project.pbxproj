// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		17E7A2D72E7DC4F000A7FCBC /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 17E7A2C12E7DC4EE00A7FCBC /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 17E7A2C82E7DC4EE00A7FCBC;
			remoteInfo = testAI;
		};
		17E7A2E12E7DC4F000A7FCBC /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 17E7A2C12E7DC4EE00A7FCBC /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 17E7A2C82E7DC4EE00A7FCBC;
			remoteInfo = testAI;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		17E7A2C92E7DC4EE00A7FCBC /* testAI.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = testAI.app; sourceTree = BUILT_PRODUCTS_DIR; };
		17E7A2D62E7DC4F000A7FCBC /* testAITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = testAITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		17E7A2E02E7DC4F000A7FCBC /* testAIUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = testAIUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		17E7A2CB2E7DC4EE00A7FCBC /* testAI */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = testAI;
			sourceTree = "<group>";
		};
		17E7A2D92E7DC4F000A7FCBC /* testAITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = testAITests;
			sourceTree = "<group>";
		};
		17E7A2E32E7DC4F000A7FCBC /* testAIUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = testAIUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		17E7A2C62E7DC4EE00A7FCBC /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		17E7A2D32E7DC4F000A7FCBC /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		17E7A2DD2E7DC4F000A7FCBC /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		17E7A2C02E7DC4EE00A7FCBC = {
			isa = PBXGroup;
			children = (
				17E7A2CB2E7DC4EE00A7FCBC /* testAI */,
				17E7A2D92E7DC4F000A7FCBC /* testAITests */,
				17E7A2E32E7DC4F000A7FCBC /* testAIUITests */,
				17E7A2CA2E7DC4EE00A7FCBC /* Products */,
			);
			sourceTree = "<group>";
		};
		17E7A2CA2E7DC4EE00A7FCBC /* Products */ = {
			isa = PBXGroup;
			children = (
				17E7A2C92E7DC4EE00A7FCBC /* testAI.app */,
				17E7A2D62E7DC4F000A7FCBC /* testAITests.xctest */,
				17E7A2E02E7DC4F000A7FCBC /* testAIUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		17E7A2C82E7DC4EE00A7FCBC /* testAI */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 17E7A2EA2E7DC4F000A7FCBC /* Build configuration list for PBXNativeTarget "testAI" */;
			buildPhases = (
				17E7A2C52E7DC4EE00A7FCBC /* Sources */,
				17E7A2C62E7DC4EE00A7FCBC /* Frameworks */,
				17E7A2C72E7DC4EE00A7FCBC /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				17E7A2CB2E7DC4EE00A7FCBC /* testAI */,
			);
			name = testAI;
			packageProductDependencies = (
			);
			productName = testAI;
			productReference = 17E7A2C92E7DC4EE00A7FCBC /* testAI.app */;
			productType = "com.apple.product-type.application";
		};
		17E7A2D52E7DC4F000A7FCBC /* testAITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 17E7A2ED2E7DC4F000A7FCBC /* Build configuration list for PBXNativeTarget "testAITests" */;
			buildPhases = (
				17E7A2D22E7DC4F000A7FCBC /* Sources */,
				17E7A2D32E7DC4F000A7FCBC /* Frameworks */,
				17E7A2D42E7DC4F000A7FCBC /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				17E7A2D82E7DC4F000A7FCBC /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				17E7A2D92E7DC4F000A7FCBC /* testAITests */,
			);
			name = testAITests;
			packageProductDependencies = (
			);
			productName = testAITests;
			productReference = 17E7A2D62E7DC4F000A7FCBC /* testAITests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		17E7A2DF2E7DC4F000A7FCBC /* testAIUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 17E7A2F02E7DC4F000A7FCBC /* Build configuration list for PBXNativeTarget "testAIUITests" */;
			buildPhases = (
				17E7A2DC2E7DC4F000A7FCBC /* Sources */,
				17E7A2DD2E7DC4F000A7FCBC /* Frameworks */,
				17E7A2DE2E7DC4F000A7FCBC /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				17E7A2E22E7DC4F000A7FCBC /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				17E7A2E32E7DC4F000A7FCBC /* testAIUITests */,
			);
			name = testAIUITests;
			packageProductDependencies = (
			);
			productName = testAIUITests;
			productReference = 17E7A2E02E7DC4F000A7FCBC /* testAIUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		17E7A2C12E7DC4EE00A7FCBC /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 2600;
				LastUpgradeCheck = 2600;
				TargetAttributes = {
					17E7A2C82E7DC4EE00A7FCBC = {
						CreatedOnToolsVersion = 26.0;
					};
					17E7A2D52E7DC4F000A7FCBC = {
						CreatedOnToolsVersion = 26.0;
						TestTargetID = 17E7A2C82E7DC4EE00A7FCBC;
					};
					17E7A2DF2E7DC4F000A7FCBC = {
						CreatedOnToolsVersion = 26.0;
						TestTargetID = 17E7A2C82E7DC4EE00A7FCBC;
					};
				};
			};
			buildConfigurationList = 17E7A2C42E7DC4EE00A7FCBC /* Build configuration list for PBXProject "testAI" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 17E7A2C02E7DC4EE00A7FCBC;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 17E7A2CA2E7DC4EE00A7FCBC /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				17E7A2C82E7DC4EE00A7FCBC /* testAI */,
				17E7A2D52E7DC4F000A7FCBC /* testAITests */,
				17E7A2DF2E7DC4F000A7FCBC /* testAIUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		17E7A2C72E7DC4EE00A7FCBC /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		17E7A2D42E7DC4F000A7FCBC /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		17E7A2DE2E7DC4F000A7FCBC /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		17E7A2C52E7DC4EE00A7FCBC /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		17E7A2D22E7DC4F000A7FCBC /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		17E7A2DC2E7DC4F000A7FCBC /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		17E7A2D82E7DC4F000A7FCBC /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 17E7A2C82E7DC4EE00A7FCBC /* testAI */;
			targetProxy = 17E7A2D72E7DC4F000A7FCBC /* PBXContainerItemProxy */;
		};
		17E7A2E22E7DC4F000A7FCBC /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 17E7A2C82E7DC4EE00A7FCBC /* testAI */;
			targetProxy = 17E7A2E12E7DC4F000A7FCBC /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		17E7A2E82E7DC4F000A7FCBC /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = RMX32XVT8F;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 26.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		17E7A2E92E7DC4F000A7FCBC /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = RMX32XVT8F;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 26.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		17E7A2EB2E7DC4F000A7FCBC /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = RMX32XVT8F;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.jxxx.testAI;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRING_CATALOG_GENERATE_SYMBOLS = YES;
				SWIFT_APPROACHABLE_CONCURRENCY = YES;
				SWIFT_DEFAULT_ACTOR_ISOLATION = MainActor;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_UPCOMING_FEATURE_MEMBER_IMPORT_VISIBILITY = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		17E7A2EC2E7DC4F000A7FCBC /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = RMX32XVT8F;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.jxxx.testAI;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRING_CATALOG_GENERATE_SYMBOLS = YES;
				SWIFT_APPROACHABLE_CONCURRENCY = YES;
				SWIFT_DEFAULT_ACTOR_ISOLATION = MainActor;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_UPCOMING_FEATURE_MEMBER_IMPORT_VISIBILITY = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		17E7A2EE2E7DC4F000A7FCBC /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = RMX32XVT8F;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 26.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.jxxx.testAITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRING_CATALOG_GENERATE_SYMBOLS = NO;
				SWIFT_APPROACHABLE_CONCURRENCY = YES;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_UPCOMING_FEATURE_MEMBER_IMPORT_VISIBILITY = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/testAI.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/testAI";
			};
			name = Debug;
		};
		17E7A2EF2E7DC4F000A7FCBC /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = RMX32XVT8F;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 26.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.jxxx.testAITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRING_CATALOG_GENERATE_SYMBOLS = NO;
				SWIFT_APPROACHABLE_CONCURRENCY = YES;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_UPCOMING_FEATURE_MEMBER_IMPORT_VISIBILITY = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/testAI.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/testAI";
			};
			name = Release;
		};
		17E7A2F12E7DC4F000A7FCBC /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = RMX32XVT8F;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.jxxx.testAIUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRING_CATALOG_GENERATE_SYMBOLS = NO;
				SWIFT_APPROACHABLE_CONCURRENCY = YES;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_UPCOMING_FEATURE_MEMBER_IMPORT_VISIBILITY = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = testAI;
			};
			name = Debug;
		};
		17E7A2F22E7DC4F000A7FCBC /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = RMX32XVT8F;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.jxxx.testAIUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRING_CATALOG_GENERATE_SYMBOLS = NO;
				SWIFT_APPROACHABLE_CONCURRENCY = YES;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_UPCOMING_FEATURE_MEMBER_IMPORT_VISIBILITY = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = testAI;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		17E7A2C42E7DC4EE00A7FCBC /* Build configuration list for PBXProject "testAI" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				17E7A2E82E7DC4F000A7FCBC /* Debug */,
				17E7A2E92E7DC4F000A7FCBC /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		17E7A2EA2E7DC4F000A7FCBC /* Build configuration list for PBXNativeTarget "testAI" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				17E7A2EB2E7DC4F000A7FCBC /* Debug */,
				17E7A2EC2E7DC4F000A7FCBC /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		17E7A2ED2E7DC4F000A7FCBC /* Build configuration list for PBXNativeTarget "testAITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				17E7A2EE2E7DC4F000A7FCBC /* Debug */,
				17E7A2EF2E7DC4F000A7FCBC /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		17E7A2F02E7DC4F000A7FCBC /* Build configuration list for PBXNativeTarget "testAIUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				17E7A2F12E7DC4F000A7FCBC /* Debug */,
				17E7A2F22E7DC4F000A7FCBC /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 17E7A2C12E7DC4EE00A7FCBC /* Project object */;
}
